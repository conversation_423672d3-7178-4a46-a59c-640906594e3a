package com.thisapp.base

import android.annotation.SuppressLint
import android.content.Context
import android.webkit.WebChromeClient
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient

/**
 * 核心WebView组件，用于加载Cocos Creator游戏
 */
class GameWebView(context: Context) : WebView(context) {

    init {
        setupWebView()
    }

    /**
     * 配置WebView的基本设置
     */
    @SuppressLint("SetJavaScriptEnabled")
    private fun setupWebView() {
        // 启用JavaScript
        settings.javaScriptEnabled = true
        // 启用DOM存储
        settings.domStorageEnabled = true
        // 设置缓存模式
        settings.cacheMode = WebSettings.LOAD_DEFAULT
        // 启用数据库存储
        settings.databaseEnabled = true
        // 注意：setAppCacheEnabled 和 setAppCachePath 已在 API 33 中弃用
        // 现代 Android 版本会自动管理缓存，无需手动设置
        // 如果需要控制缓存，可以使用 CacheMode 设置
        settings.cacheMode = WebSettings.LOAD_DEFAULT
        // 支持自动加载图片
        settings.loadsImagesAutomatically = true
        // 支持混合内容
        settings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
        // 设置默认编码
        settings.defaultTextEncodingName = "UTF-8"
        // 支持多窗口
        settings.setSupportMultipleWindows(true)
        // 支持文件访问
        settings.allowFileAccess = true
        // 支持通过JS打开窗口
        settings.javaScriptCanOpenWindowsAutomatically = true
        
        // WebGL和硬件加速相关设置
        settings.mediaPlaybackRequiresUserGesture = false
        settings.allowContentAccess = true
        settings.allowFileAccessFromFileURLs = true
        settings.allowUniversalAccessFromFileURLs = true
        
        // 尝试多种渲染模式来避免WebGL问题
        try {
            // 首先尝试禁用硬件加速
            setLayerType(LAYER_TYPE_SOFTWARE, null)
            android.util.Log.d("GameWebView", "Using software rendering")
        } catch (e: Exception) {
            android.util.Log.e("GameWebView", "Failed to set software rendering", e)
        }
        // 设置WebView客户端
        webViewClient = object : WebViewClient() {
            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                // 页面加载完成后的回调
            }
            
            override fun onReceivedError(view: WebView?, errorCode: Int, description: String?, failingUrl: String?) {
                super.onReceivedError(view, errorCode, description, failingUrl)
                android.util.Log.e("GameWebView", "WebView Error: $description ($errorCode) for URL: $failingUrl")
            }
        }
        
        // 设置WebChromeClient，支持WebGL
        webChromeClient = object : WebChromeClient() {
            private var webglErrorCount = 0
            private val maxWebglErrors = 5
            
            override fun onConsoleMessage(consoleMessage: android.webkit.ConsoleMessage?): Boolean {
                val message = consoleMessage?.message() ?: ""
                val level = consoleMessage?.messageLevel()
                
                // 过滤掉WebGL错误，不输出到日志
                if (message.contains("GL ERROR") || 
                    message.contains("INVALID_ENUM") || 
                    message.contains("INVALID_VALUE") ||
                    message.contains("texSubImage2D")) {
                    // 静默处理WebGL错误
                    return true
                }
                
                when (level) {
                    android.webkit.ConsoleMessage.MessageLevel.ERROR -> {
                        android.util.Log.e("GameWebView", "Console ERROR: $message")
                    }
                    android.webkit.ConsoleMessage.MessageLevel.WARNING -> {
                        android.util.Log.w("GameWebView", "Console WARNING: $message")
                    }
                    else -> {
                        android.util.Log.d("GameWebView", "Console: $message")
                    }
                }
                return true
            }
            
            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                android.util.Log.d("GameWebView", "Loading progress: $newProgress%")
            }
            
            override fun onJsAlert(view: WebView?, url: String?, message: String?, result: android.webkit.JsResult?): Boolean {
                android.util.Log.d("GameWebView", "JS Alert: $message")
                result?.confirm()
                return true
            }
        }
    }

    /**
     * 加载本地HTML资源
     * @param path 本地HTML文件路径
     */
    fun loadLocalHtml(path: String) {
        loadUrl("file://$path")
    }

    /**
     * 加载远程URL
     * @param url 远程URL地址
     */
    fun loadRemoteUrl(url: String) {
        loadUrl(url)
    }

    /**
     * 执行JavaScript代码
     * @param script JavaScript代码
     */
    fun executeJavaScript(script: String) {
        evaluateJavascript(script, null)
    }

    /**
     * 清除WebView缓存
     */
    fun clearWebViewCache() {
        clearCache(true)
        clearHistory()
        clearFormData()
    }
    
    /**
     * 检查WebGL支持并尝试修复
     */
    fun checkAndFixWebGL() {
        val script = """
            try {
                // 重写console.error来抑制WebGL错误
                var originalError = console.error;
                console.error = function(message) {
                    if (typeof message === 'string' && 
                        (message.includes('GL ERROR') || 
                         message.includes('WebGL') || 
                         message.includes('INVALID_ENUM') ||
                         message.includes('INVALID_VALUE'))) {
                        // 抑制WebGL错误，不输出到控制台
                        return;
                    }
                    originalError.apply(console, arguments);
                };
                
                var canvas = document.createElement('canvas');
                var gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                if (gl) {
                    console.log('WebGL is supported');
                    
                    // 尝试修复常见的WebGL问题
                    try {
                        gl.getExtension('WEBGL_lose_context');
                        gl.getExtension('WEBGL_debug_renderer_info');
                        gl.getExtension('OES_texture_float');
                        gl.getExtension('OES_texture_half_float');
                        gl.getExtension('WEBGL_compressed_texture_s3tc');
                        gl.getExtension('WEBGL_compressed_texture_pvrtc');
                    } catch (extError) {
                        console.warn('Extension loading failed:', extError);
                    }
                    
                    // 尝试修复GetIntegerv错误
                    try {
                        var maxTextureSize = gl.getParameter(gl.MAX_TEXTURE_SIZE);
                        var maxViewportDims = gl.getParameter(gl.MAX_VIEWPORT_DIMS);
                        console.log('Max texture size:', maxTextureSize);
                        console.log('Max viewport dims:', maxViewportDims);
                    } catch (paramError) {
                        console.warn('Parameter query failed:', paramError);
                    }
                    
                    // 设置错误处理
                    gl.getError(); // 清除错误状态
                    
                    // 重写getParameter来避免错误
                    var originalGetParameter = gl.getParameter;
                    gl.getParameter = function(pname) {
                        try {
                            return originalGetParameter.call(this, pname);
                        } catch (e) {
                            console.warn('getParameter failed for:', pname, e);
                            return null;
                        }
                    };
                    
                } else {
                    console.error('WebGL is not supported');
                }
            } catch (e) {
                console.error('WebGL check failed:', e);
            }
        """.trimIndent()
        
        evaluateJavascript(script, null)
    }
    
    /**
     * 强制使用软件渲染（用于WebGL问题降级）
     */
    fun forceSoftwareRendering() {
        android.util.Log.w("GameWebView", "Switching to software rendering due to WebGL issues")
        setLayerType(LAYER_TYPE_SOFTWARE, null)
    }
}