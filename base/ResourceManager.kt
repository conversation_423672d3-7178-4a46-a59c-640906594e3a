package com.thisapp.base

import android.content.Context
import android.util.Log
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.security.MessageDigest
import java.util.concurrent.TimeUnit
import kotlin.concurrent.thread

/**
 * 资源管理器，负责管理本地和远程资源
 */
class ResourceManager(private val context: Context) {
    
    private val TAG = "ResourceManager"
    private val okHttpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()
    
    // 本地资源根目录
    private val localResourceDir: File by lazy {
        File(context.filesDir, "web_resources").apply {
            if (!exists()) mkdirs()
        }
    }
    
    // 版本信息文件
    private val versionFile: File by lazy {
        File(localResourceDir, "version.json")
    }
    
    /**
     * 检查远程资源是否有更新
     * @param remoteVersionUrl 远程版本信息URL
     * @return 是否有更新
     */
    fun checkForUpdates(remoteVersionUrl: String): Boolean {
        try {
            // 获取远程版本信息
            val remoteVersionJson = fetchRemoteContent(remoteVersionUrl) ?: return false
            
            // 如果本地版本文件不存在，则需要更新
            if (!versionFile.exists()) {
                return true
            }
            
            // 比较本地和远程版本
            val localVersionJson = versionFile.readText()
            return localVersionJson != remoteVersionJson
        } catch (e: Exception) {
            Log.e(TAG, "Error checking for updates", e)
            return false
        }
    }
    
    /**
     * 下载远程资源到本地
     * @param remoteBaseUrl 远程资源基础URL
     * @param remoteVersionUrl 远程版本信息URL
     * @param callback 下载完成回调
     */
    fun downloadResources(remoteBaseUrl: String, remoteVersionUrl: String, callback: (Boolean) -> Unit) {
        thread {
            try {
                // 获取远程版本信息
                val remoteVersionJson = fetchRemoteContent(remoteVersionUrl) ?: run {
                    callback(false)
                    return@thread
                }
                
                // 解析版本信息中的文件列表
                val fileList = parseFileList(remoteVersionJson)
                
                // 下载所有文件
                var success = true
                for (filePath in fileList) {
                    val remoteFileUrl = "$remoteBaseUrl/$filePath"
                    val localFile = File(localResourceDir, filePath)
                    
                    // 确保目录存在
                    localFile.parentFile?.mkdirs()
                    
                    // 下载文件
                    if (!downloadFile(remoteFileUrl, localFile)) {
                        success = false
                        break
                    }
                }
                
                // 如果所有文件下载成功，保存版本信息
                if (success) {
                    versionFile.writeText(remoteVersionJson)
                }
                
                callback(success)
            } catch (e: Exception) {
                Log.e(TAG, "Error downloading resources", e)
                callback(false)
            }
        }
    }
    
    /**
     * 获取本地资源路径
     * @return 本地资源路径
     */
    fun getLocalResourcePath(): String {
        return localResourceDir.absolutePath
    }
    
    /**
     * 获取本地资源入口文件路径
     * @param entryFileName 入口文件名，默认为index.html
     * @return 入口文件路径
     */
    fun getLocalEntryFilePath(entryFileName: String = "index.html"): String {
        return File(localResourceDir, entryFileName).absolutePath
    }
    
    /**
     * 检查本地资源是否存在
     * @param entryFileName 入口文件名，默认为index.html
     * @return 本地资源是否存在
     */
    fun isLocalResourceAvailable(entryFileName: String = "index.html"): Boolean {
        return File(localResourceDir, entryFileName).exists() && versionFile.exists()
    }
    
    /**
     * 清除本地资源
     */
    fun clearLocalResources() {
        localResourceDir.deleteRecursively()
        localResourceDir.mkdirs()
    }
    
    /**
     * 获取远程内容
     * @param url 远程URL
     * @return 内容字符串
     */
    private fun  fetchRemoteContent(url: String): String? {
        try {
            val request = Request.Builder().url(url).build()
            val response = okHttpClient.newCall(request).execute()
            if (!response.isSuccessful) {
                return null
            }
            return response.body?.string()
        } catch (e: IOException) {
            Log.e(TAG, "Error fetching remote content", e)
            return null
        }
    }
    
    /**
     * 下载文件
     * @param url 远程URL
     * @param destFile 目标文件
     * @return 是否下载成功
     */
    private fun downloadFile(url: String, destFile: File): Boolean {
        var response: Response? = null
        try {
            val request = Request.Builder().url(url).build()
            response = okHttpClient.newCall(request).execute()
            if (!response.isSuccessful) {
                return false
            }
            
            val body = response.body ?: return false
            val inputStream = body.byteStream()
            val outputStream = FileOutputStream(destFile)
            
            val buffer = ByteArray(4096)
            var bytesRead: Int
            while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                outputStream.write(buffer, 0, bytesRead)
            }
            
            outputStream.close()
            inputStream.close()
            return true
        } catch (e: IOException) {
            Log.e(TAG, "Error downloading file", e)
            return false
        } finally {
            response?.close()
        }
    }
    
    /**
     * 解析版本信息中的文件列表
     * @param versionJson 版本信息JSON
     * @return 文件路径列表
     */
    private fun parseFileList(versionJson: String): List<String> {
        try {
            // 简单实现，实际项目中应该使用JSON解析库
            // 这里假设版本信息JSON格式为：{"files":["file1.html","file2.js",...]}
            val fileListRegex = "\"files\":\\s*\\[(.*?)\\]".toRegex()
            val fileRegex = "\"(.*?)\"".toRegex()
            
            val fileListMatch = fileListRegex.find(versionJson)
            val fileListString = fileListMatch?.groupValues?.get(1) ?: return emptyList()
            
            return fileRegex.findAll(fileListString)
                .map { it.groupValues[1] }
                .toList()
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing file list from non-JSON input", e)
            return emptyList()
        }
    }
    
    /**
     * 计算文件的MD5哈希值
     * @param file 文件
     * @return MD5哈希值
     */
    private fun calculateMD5(file: File): String {
        val md = MessageDigest.getInstance("MD5")
        val buffer = ByteArray(8192)
        val inputStream = file.inputStream()
        
        var bytesRead: Int
        while (inputStream.read(buffer).also { bytesRead = it } != -1) {
            md.update(buffer, 0, bytesRead)
        }
        
        inputStream.close()
        
        val md5Bytes = md.digest()
        return md5Bytes.joinToString("") { "%02x".format(it) }
    }
}