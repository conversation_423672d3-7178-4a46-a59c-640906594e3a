package com.thisapp.webview


import android.os.Bundle
import android.widget.FrameLayout
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.thisapp.base.GameWebView
import com.thisapp.base.GameLoader
import com.thisapp.base.ResourceManager

class MainActivity : AppCompatActivity() {

    private lateinit var webViewContainer: FrameLayout

    private lateinit var cocosWebView: GameWebView
    private lateinit var resourceManager: ResourceManager
    private lateinit var gameLoader: GameLoader


    private val remoteBaseUrl = "https://api.dev.ylymgame.cn:8443" // 替换为实际的远程游戏URL

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        initViews()
        setupWebView()
        loadGame()
    }

    private fun initViews() {
        webViewContainer = findViewById(R.id.webview_container)

    }

    private fun setupWebView() {
        // 初始化WebView
        cocosWebView = GameWebView(this)
        webViewContainer.addView(cocosWebView)
        
        // 延迟检查WebGL支持并注入错误抑制脚本
        webViewContainer.postDelayed({
            cocosWebView.checkAndFixWebGL()
            injectWebGLErrorSuppression()
        }, 2000) // 2秒后检查WebGL

        // 初始化资源管理器
        resourceManager = ResourceManager(this)
        
        // 初始化游戏加载器
        gameLoader = GameLoader(this, cocosWebView, resourceManager)
        gameLoader.init(
            remoteBaseUrl = remoteBaseUrl,
            remoteVersionUrl = "$remoteBaseUrl/version.json",
            entryFileName = "index.html"
        )
    }


    private fun loadGame(forceRemote: Boolean = false, loadTestPage: Boolean = false) {
        if (loadTestPage) {
            Toast.makeText(this, "正在加载WebGL测试页面...", Toast.LENGTH_SHORT).show()
            android.util.Log.d("MainActivity", "Loading WebGL test page")
            cocosWebView.loadUrl("file:///android_asset/test_webgl.html")
        } else {
            Toast.makeText(this, "正在加载远程游戏...", Toast.LENGTH_SHORT).show()
            android.util.Log.d("MainActivity", "Loading remote game from: $remoteBaseUrl")
            gameLoader.loadGame(forceRemote) { success ->
                runOnUiThread {
                    android.util.Log.d("MainActivity", "Game load result: $success")
                    if (success) {
                        Toast.makeText(this, "游戏加载成功", Toast.LENGTH_SHORT).show()
                    } else {
                        Toast.makeText(this, "游戏加载失败，尝试加载WebGL测试页面", Toast.LENGTH_SHORT).show()
                        android.util.Log.d("MainActivity", "Falling back to WebGL test page")
                        // 如果远程游戏加载失败，自动加载测试页面
                        cocosWebView.loadUrl("file:///android_asset/test_webgl.html")
                    }
                }
            }
        }
    }
    
    private fun injectWebGLErrorSuppression() {
        val script = """
            (function() {
                // 重写console.error来完全抑制WebGL错误
                var originalConsoleError = console.error;
                console.error = function() {
                    var message = arguments[0];
                    if (typeof message === 'string' && 
                        (message.includes('GL ERROR') || 
                         message.includes('WebGL') || 
                         message.includes('INVALID_ENUM') ||
                         message.includes('INVALID_VALUE') ||
                         message.includes('texSubImage2D'))) {
                        // 完全忽略WebGL错误
                        return;
                    }
                    originalConsoleError.apply(console, arguments);
                };
                
                // 重写console.warn来抑制WebGL警告
                var originalConsoleWarn = console.warn;
                console.warn = function() {
                    var message = arguments[0];
                    if (typeof message === 'string' && 
                        (message.includes('GL ERROR') || 
                         message.includes('WebGL') || 
                         message.includes('INVALID_ENUM') ||
                         message.includes('INVALID_VALUE'))) {
                        return;
                    }
                    originalConsoleWarn.apply(console, arguments);
                };
                
                console.log('WebGL error suppression injected');
            })();
        """.trimIndent()
        
        cocosWebView.evaluateJavascript(script, null)
    }

    override fun onDestroy() {
        // 清理WebView资源
        webViewContainer.removeAllViews()
        cocosWebView.destroy()
        super.onDestroy()
    }
}